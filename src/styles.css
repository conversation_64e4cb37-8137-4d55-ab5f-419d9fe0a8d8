/* You can add global styles to this file, and also import other style files */
@import "tailwindcss";

/* Dark Theme Color Variables */
:root {
  /* Primary Dark Colors */
  --dark-primary: #0a0a0f;
  --dark-secondary: #0f0f1a;
  --dark-tertiary: #1a1a2e;
  --dark-accent: #16213e;

  /* Darker Purple Shades */
  --dark-purple-900: #1e1b4b;
  --dark-purple-800: #312e81;
  --dark-purple-700: #3730a3;
  --dark-purple-600: #4338ca;
  --dark-purple-500: #6366f1;

  /* Darker Blue Shades */
  --dark-blue-900: #0c1426;
  --dark-blue-800: #1e293b;
  --dark-blue-700: #334155;
  --dark-blue-600: #475569;

  /* Darker Gray Shades */
  --dark-gray-900: #0f0f0f;
  --dark-gray-800: #1a1a1a;
  --dark-gray-700: #262626;
  --dark-gray-600: #404040;

  /* Accent Colors */
  --dark-accent-purple: #8b5cf6;
  --dark-accent-pink: #ec4899;
  --dark-accent-blue: #3b82f6;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --text-muted: rgba(255, 255, 255, 0.6);
  --text-disabled: rgba(255, 255, 255, 0.4);
}

/* Dark Theme Classes */
.theme-dark-primary { background-color: var(--dark-primary); }
.theme-dark-secondary { background-color: var(--dark-secondary); }
.theme-dark-tertiary { background-color: var(--dark-tertiary); }
.theme-dark-accent { background-color: var(--dark-accent); }

.theme-dark-purple-900 { background-color: var(--dark-purple-900); }
.theme-dark-purple-800 { background-color: var(--dark-purple-800); }
.theme-dark-purple-700 { background-color: var(--dark-purple-700); }

.theme-dark-blue-900 { background-color: var(--dark-blue-900); }
.theme-dark-blue-800 { background-color: var(--dark-blue-800); }

.theme-dark-gray-900 { background-color: var(--dark-gray-900); }
.theme-dark-gray-800 { background-color: var(--dark-gray-800); }

/* Text Color Classes */
.theme-text-primary { color: var(--text-primary); }
.theme-text-secondary { color: var(--text-secondary); }
.theme-text-muted { color: var(--text-muted); }

/* Gradient Classes */
.theme-gradient-primary {
  background: linear-gradient(135deg, var(--dark-primary) 0%, var(--dark-secondary) 25%, var(--dark-tertiary) 50%, var(--dark-accent) 75%, var(--dark-primary) 100%);
}

.theme-gradient-purple {
  background: linear-gradient(135deg, var(--dark-purple-900) 0%, var(--dark-purple-800) 50%, var(--dark-purple-700) 100%);
}

.theme-gradient-blue {
  background: linear-gradient(135deg, var(--dark-blue-900) 0%, var(--dark-blue-800) 50%, var(--dark-blue-700) 100%);
}

.theme-gradient-text {
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--dark-accent-purple) 50%, var(--dark-accent-pink) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Global styles for smooth scrolling and better typography */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  background-color: var(--dark-primary);
  color: var(--text-primary);
}

