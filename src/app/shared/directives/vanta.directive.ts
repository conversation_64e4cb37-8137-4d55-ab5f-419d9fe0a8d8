import { Directive, ElementRef, Input, OnInit, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit } from '@angular/core';

declare var VANTA: any;
declare var THREE: any;

@Directive({
  selector: '[appVanta]',
  standalone: true
})
export class VantaDirective implements OnInit, OnD<PERSON>roy, AfterViewInit {
  @Input() vantaOptions: any = {};
  
  private vantaEffect: any;

  constructor(private el: ElementRef) {}

  ngOnInit() {}

  ngAfterViewInit() {
    // Check if VANTA and THREE are loaded
    if (typeof VANTA !== 'undefined' && typeof THREE !== 'undefined') {
      this.initVanta();
    } else {
      // Load scripts dynamically
      this.loadVantaScripts().then(() => {
        this.initVanta();
      });
    }
  }

  ngOnDestroy() {
    if (this.vantaEffect) {
      this.vantaEffect.destroy();
    }
  }

  private async loadVantaScripts(): Promise<void> {
    return new Promise((resolve) => {
      // Load THREE.js
      if (typeof THREE === 'undefined') {
        const threeScript = document.createElement('script');
        threeScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js';
        threeScript.onload = () => {
          // Load VANTA.js NET effect
          const vantaScript = document.createElement('script');
          vantaScript.src = 'https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.net.min.js';
          vantaScript.onload = () => resolve();
          document.head.appendChild(vantaScript);
        };
        document.head.appendChild(threeScript);
      } else {
        resolve();
      }
    });
  }

  private initVanta() {
    if (this.vantaEffect) {
      this.vantaEffect.destroy();
    }

    const defaultOptions = {
      el: this.el.nativeElement,
      mouseControls: true,
      touchControls: true,
      gyroControls: false,
      minHeight: 200.00,
      minWidth: 200.00,
      scale: 1.00,
      scaleMobile: 1.00,
      color: 0x2563eb,
      backgroundColor: 0x0f172a,
      points: 12.00,
      maxDistance: 20.00,
      spacing: 18.00
    };

    const options = { ...defaultOptions, ...this.vantaOptions };
    
    if (typeof VANTA !== 'undefined' && VANTA.NET) {
      this.vantaEffect = VANTA.NET(options);
    }
  }
}
