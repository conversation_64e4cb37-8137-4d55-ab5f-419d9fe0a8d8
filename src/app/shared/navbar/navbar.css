/* Ensure padding works properly for the header */
header {
  padding: 1.5rem !important;
  box-sizing: border-box;
}

/* Custom PrimeNG Menubar Styling */
:host ::ng-deep .custom-menubar {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
}

:host ::ng-deep .custom-menubar .p-menubar-root-list {
  background: transparent !important;
  border: none !important;
  gap: 2rem;
}

:host ::ng-deep .custom-menubar .p-menuitem-link {
  background: transparent !important;
  color: var(--text-secondary) !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem 1rem !important;
  transition: all 0.3s ease !important;
  font-weight: 500 !important;
  position: relative !important;
}

:host ::ng-deep .custom-menubar .p-menuitem-link:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: var(--dark-accent-purple) !important;
  transform: translateY(-2px) !important;
}

:host ::ng-deep .custom-menubar .p-menuitem-link:focus {
  box-shadow: 0 0 0 2px var(--dark-accent-purple) !important;
}

:host ::ng-deep .custom-menubar .p-menuitem-icon {
  display: none !important; /* Hide icons for cleaner look */
}

:host ::ng-deep .custom-menubar .p-menuitem-text {
  font-size: 1rem !important;
}

/* Hover underline effect */
:host ::ng-deep .custom-menubar .p-menuitem-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, var(--dark-accent-purple) 0%, var(--dark-accent-pink) 100%);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

:host ::ng-deep .custom-menubar .p-menuitem-link:hover::after {
  width: 80%;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  :host ::ng-deep .custom-menubar {
    display: none !important;
  }
}