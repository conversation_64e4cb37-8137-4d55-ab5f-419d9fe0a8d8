/* Ensure padding works properly for the header */
header {
  padding: 1.5rem !important;
  box-sizing: border-box;
}

/* Simple navbar styling */
nav ul li a {
  text-decoration: none;
  display: block;
  transition: all 0.3s ease;
}

nav ul li a:hover {
  text-decoration: none;
}

/* Focus states for accessibility */
nav ul li a:focus {
  outline: 2px solid var(--dark-accent-purple);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Mobile menu button focus */
button:focus {
  outline: 2px solid var(--dark-accent-purple);
  outline-offset: 2px;
  border-radius: 4px;
}