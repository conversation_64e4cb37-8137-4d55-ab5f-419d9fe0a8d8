import { Component, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-navbar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './navbar.html',
  styleUrls: ['./navbar.css']
})
export class Navbar {
  readonly navItems: string[] = ['Home', 'About', 'Skills', 'Projects', 'Contact'];
  isMobileMenuOpen = false;
  activeSection = 'Home';

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  scrollToSection(sectionId: string) {
    const element = document.getElementById(sectionId);
    const navbar = document.querySelector('header');

    if (element) {
      const navbarHeight = navbar ? navbar.clientHeight : 0;
      const elementPosition = element.offsetTop - navbarHeight;

      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
      });
    }

    this.isMobileMenuOpen = false;
  }

  
  @HostListener('window:scroll', [])
  onScroll() {
    for (const section of this.navItems) {
      const el = document.getElementById(section.toLowerCase());
      if (el) {
        const rect = el.getBoundingClientRect();
        if (rect.top <= 100 && rect.bottom >= 100) {
          this.activeSection = section;
          break;
        }
      }
    }
  }
}
