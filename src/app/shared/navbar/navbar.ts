import { Component } from '@angular/core';
import { MenubarModule } from 'primeng/menubar';
import { MenuItem } from 'primeng/api';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-navbar',
  standalone: true,
  imports: [MenubarModule, CommonModule],
  templateUrl: './navbar.html',
  styleUrls: ['./navbar.css']
})
export class Navbar {
  readonly navItems: string[] = ['Home', 'About','Education', 'Skills', 'Projects', 'Contact'];
  isMobileMenuOpen = false;

  menuItems: MenuItem[] = [
    {
      label: 'Home',
      icon: 'pi pi-home',
      command: () => this.scrollToSection('home')
    },
    {
      label: 'About',
      icon: 'pi pi-user',
      command: () => this.scrollToSection('about')
    },
    {
      label: 'Education',
      icon: 'pi pi-book',
      command: () => this.scrollToSection('education')
    },
    {
      label: 'Skills',
      icon: 'pi pi-cog',
      command: () => this.scrollToSection('skills')
    },
    {
      label: 'Projects',
      icon: 'pi pi-briefcase',
      command: () => this.scrollToSection('projects')
    },
    {
      label: 'Contact',
      icon: 'pi pi-envelope',
      command: () => this.scrollToSection('contact')
    }
  ];

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  scrollToSection(sectionId: string) {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    this.isMobileMenuOpen = false; // Close mobile menu after navigation
  }
}
