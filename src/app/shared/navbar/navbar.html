<header class="w-full pt-6 pb-6 pl-6 pr-6 flex flex-row justify-between items-center theme-text-primary relative z-20 backdrop-blur-sm"
        style="background-color: var(--dark-gray-800); opacity: 0.95;">

    <!-- Logo Section -->
    <div class="flex items-center space-x-3">
        <img src="Praneeth.png" class="h-12 w-12 rounded-full object-cover border-2 border-purple-500/30" alt="Praneeth Logo">
      
    </div>

    <!-- PrimeNG Menubar -->
    <p-menubar [model]="menuItems"
               styleClass="custom-menubar bg-transparent border-none">
        <ng-template pTemplate="start">
            <!-- This will be empty since we have logo on the left -->
        </ng-template>
        <ng-template pTemplate="end">
            <!-- Optional: Add social links or CTA button here -->
            <button class="px-4 py-2 rounded-lg font-medium transition-all duration-300 hover:-translate-y-1"
                    style="background: linear-gradient(135deg, var(--dark-purple-700) 0%, var(--dark-accent-purple) 100%); color: var(--text-primary);">
                Contact Me
            </button>
        </ng-template>
    </p-menubar>

    <!-- Mobile Menu Button (fallback) -->
    <div class="md:hidden">
        <button (click)="toggleMobileMenu()"
                class="theme-text-primary transition-all duration-200 p-2 rounded-lg hover:bg-white/10"
                style="color: var(--text-primary);">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      [attr.d]="isMobileMenuOpen ? 'M6 18L18 6M6 6l12 12' : 'M4 6h16M4 12h16M4 18h16'"></path>
            </svg>
        </button>
    </div>
</header>

<!-- Mobile Menu Overlay (fallback for small screens) -->
@if (isMobileMenuOpen) {
    <div class="md:hidden fixed inset-0 z-30 transition-all duration-300 opacity-100 pointer-events-auto">
        <!-- Backdrop -->
        <div class="absolute inset-0 bg-black/50 backdrop-blur-sm" (click)="toggleMobileMenu()"></div>

        <!-- Mobile Menu Panel -->
        <div class="absolute top-0 right-0 h-full w-80 max-w-[80vw] transform transition-transform duration-300 translate-x-0"
             style="background: linear-gradient(135deg, var(--dark-gray-900) 0%, var(--dark-gray-800) 100%);">

            <!-- Mobile Menu Header -->
            <div class="flex items-center justify-between p-6 border-b border-white/10">
                <div class="flex items-center space-x-3">
                    <img src="Praneeth.png" class="h-10 w-10 rounded-full object-cover border-2 border-purple-500/30" alt="Praneeth Logo">
                    <div>
                        <h3 class="font-bold theme-gradient-text">Praneeth</h3>
                        <p class="text-xs theme-text-muted">Portfolio</p>
                    </div>
                </div>
                <button (click)="toggleMobileMenu()" class="theme-text-primary p-2 rounded-lg hover:bg-white/10">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Menu Items -->
            <nav class="p-6">
                <ul class="space-y-4">
                    @for (item of navItems; track $index) {
                        <li>
                            <a href="#{{item.toLowerCase()}}"
                               (click)="toggleMobileMenu()"
                               class="block font-medium transition-all duration-200 p-3 rounded-lg hover:bg-white/10 theme-text-secondary"
                               style="color: var(--text-secondary);">
                                {{item}}
                            </a>
                        </li>
                    }
                </ul>
            </nav>
        </div>
    </div>
}