<header
  class="w-full fixed top-0 left-0 px-6 py-4 flex flex-row justify-between items-center backdrop-blur-lg shadow-lg z-50"
  style="background: linear-gradient(90deg, rgba(30,30,46,0.95) 0%, rgba(20,20,30,0.95) 100%);"
>
  <!-- Logo Section (Text only) -->
  <div class="flex items-center">
    <h2 class="text-xl font-extrabold bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent">
      Praneeth
    </h2>
  </div>

  <!-- Desktop Navigation -->
  <nav class="hidden md:flex">
    <ul class="flex flex-row gap-8">
      @for (item of navItems; track $index) {
        <li class="relative group">
          <a
            (click)="scrollToSection(item.toLowerCase())"
            [ngClass]="{
              'text-purple-400 font-semibold': activeSection === item,
              'text-gray-300': activeSection !== item
            }"
            class="transition-all duration-300 cursor-pointer px-3 py-2 rounded-lg block"
          >
            {{ item }}
          </a>
          <div
            class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-300 group-hover:w-full"
          ></div>
        </li>
      }
    </ul>
  </nav>

  <!-- Mobile Menu Button -->
  <div class="md:hidden">
    <button
      (click)="toggleMobileMenu()"
      class="text-gray-200 transition-all duration-200 p-2 rounded-lg hover:bg-white/10"
    >
      <svg
        class="w-6 h-6"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          [attr.d]="
            isMobileMenuOpen
              ? 'M6 18L18 6M6 6l12 12'
              : 'M4 6h16M4 12h16M4 18h16'
          "
        ></path>
      </svg>
    </button>
  </div>
</header>

<!-- Mobile Menu Overlay -->
@if (isMobileMenuOpen) {
  <div
    class="md:hidden fixed inset-0 z-40 transition-all duration-300 opacity-100 pointer-events-auto"
  >
    <!-- Backdrop -->
    <div
      class="absolute inset-0 bg-black/40 backdrop-blur-sm"
      (click)="toggleMobileMenu()"
    ></div>

    <!-- Mobile Menu Panel -->
    <div
      class="absolute z-50 w-full  h-fit max-w-[80vw] transform transition-transform duration-300 translate-x-0"
      style="background: linear-gradient(135deg, rgba(25,25,35,0.95) 0%, rgba(15,15,25,0.95) 100%);"
    >
      <!-- Mobile Menu Header -->
      <div
        class="flex items-center justify-between p-6 border-b border-white/10"
      >
        <h3
          class="text-lg font-bold bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent"
        >
          Praneeth
        </h3>
        <button
          (click)="toggleMobileMenu()"
          class="text-gray-200 p-2 rounded-lg hover:bg-white/10"
        >
          <svg
            class="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
        </button>
      </div>

      <!-- Mobile Menu Items -->
      <nav class="p-6">
        <ul class="space-y-4">
          @for (item of navItems; track $index) {
            <li>
              <a
                (click)="scrollToSection(item.toLowerCase())"
                [ngClass]="{
                  'text-purple-400 font-semibold': activeSection === item,
                  'text-gray-300': activeSection !== item
                }"
                class="block transition-all duration-200 p-3 rounded-lg hover:bg-white/10"
              >
                {{ item }}
              </a>
            </li>
          }
        </ul>
      </nav>
    </div>
  </div>
}
