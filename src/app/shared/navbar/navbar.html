<header class="w-full pt-6 pb-6 pl-6 pr-6 flex flex-row justify-between items-center theme-text-primary relative z-20 backdrop-blur-sm" >
    <div class="flex items-center">
        <img src="text.svg" class="h-10 w-10" alt="logo">
    </div>
    <nav class="hidden md:block">
        <ul class="flex flex-row space-x-8">
            @for (item of navItems; track $index) {
                <li class="font-medium transition-colors duration-200 cursor-pointer theme-text-secondary hover:theme-text-primary"
                    style="color: var(--text-secondary);"
                    onmouseover="this.style.color='var(--dark-accent-purple)'"
                    onmouseout="this.style.color='var(--text-secondary)'">
                    {{item}}
                </li>
            }
        </ul>
    </nav>
    <div class="md:hidden">
        <button class="theme-text-primary transition-colors duration-200"
                style="color: var(--text-primary);"
                onmouseover="this.style.color='var(--dark-accent-purple)'"
                onmouseout="this.style.color='var(--text-primary)'">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>
    </div>
</header>