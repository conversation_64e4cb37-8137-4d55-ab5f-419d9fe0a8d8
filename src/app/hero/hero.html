
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden flex flex-col">
 
  <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/5 to-pink-500/10 pointer-events-none"></div>
  <div class="absolute top-20 left-20 w-72 h-72 bg-purple-500/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-20 right-20 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl"></div>


  <app-navbar></app-navbar>
  <div class="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 relative z-10">
    <div class="text-center max-w-4xl">
      <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent leading-tight">
        Welcome to My Portfolio
      </h1>
      <p class="text-lg sm:text-xl lg:text-2xl mb-10 text-white/80 font-light max-w-2xl mx-auto leading-relaxed">
        Full Stack Developer & Creative Problem Solver
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <button class="w-full sm:w-auto px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-lg shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40 hover:-translate-y-1 transition-all duration-300 ease-out">
          View My Work
        </button>
        <button class="w-full sm:w-auto px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-lg border border-white/20 hover:bg-white/20 hover:border-white/30 hover:-translate-y-1 transition-all duration-300 ease-out">
          Contact Me
        </button>
      </div>
    </div>
  </div>
</div>
