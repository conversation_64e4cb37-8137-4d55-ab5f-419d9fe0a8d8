<div
  appVanta
  [vantaOptions]="vantaOptions"
  class="min-h-screen relative overflow-hidden flex flex-col"
>
  <!-- Background overlay with darker theme gradients -->
  <div
    class="absolute inset-0 pointer-events-none"
    style="
      background: linear-gradient(
        45deg,
        var(--dark-blue-900) 0%,
        transparent 30%,
        var(--dark-purple-900) 70%,
        transparent 100%
      );
    "
  ></div>
  <div
    class="absolute top-20 left-20 w-72 h-72 rounded-full blur-3xl"
    style="background-color: var(--dark-purple-800); opacity: 0.3"
  ></div>
  <div
    class="absolute bottom-20 right-20 w-96 h-96 rounded-full blur-3xl"
    style="background-color: var(--dark-accent-purple); opacity: 0.15"
  ></div>
  <div
    class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[400px] rounded-full blur-3xl"
    style="
      background: radial-gradient(circle, var(--dark-purple-700) 0%, transparent 70%);
      opacity: 0.1;
    "
  ></div>

  <app-navbar></app-navbar>
  <div class="flex gap-10 flex-col md:flex-row items-center justify-center px-4 sm:px-6 lg:px-8 relative z-10 md:items-center md:justify-center">
    <div>
      <img src="Praneeth.png" class="h-56 rounded-full" alt="praneeth" />
    </div>
    <div class="max-w-4xl m-4">
      <p class="text-sm tracking-wider text-gray-400 uppercase">
        Developer | Angular | Full-Stack | Node Js
      </p>
      <h1 class="mt-2 text-4xl md:text-6xl font-extrabold leading-tight">
        Hi, I’m
        <span
          class="bg-gradient-to-r from-fuchsia-500 via-purple-500 to-indigo-500 bg-clip-text text-transparent"
        >
          Praneeth Kumar
        </span>
      </h1>
      <p
        class="text-lg sm:text-xl lg:text-2xl mb-10 theme-text-secondary font-light max-w-2xl mx-auto leading-relaxed"
      >
        Full Stack Developer.
      </p>
      <p
        class="text-lg sm:text-xl lg:text-xl mb-10 theme-text-secondary font-light max-w-2xl mx-auto leading-relaxed"
      >
        I build scalable, user-friendly apps with clean code & creativity.
      </p>
    </div>
  </div>
</div>
