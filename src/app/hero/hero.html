
<div appVanta [options]="vantaOptions" class="min-h-screen theme-gradient-primary relative overflow-hidden flex flex-col">
  <!-- Background overlay with darker theme gradients -->
  <div class="absolute inset-0 pointer-events-none" style="background: linear-gradient(45deg, var(--dark-blue-900) 0%, transparent 30%, var(--dark-purple-900) 70%, transparent 100%);"></div>
  <div class="absolute top-20 left-20 w-72 h-72 rounded-full blur-3xl" style="background-color: var(--dark-purple-800); opacity: 0.3;"></div>
  <div class="absolute bottom-20 right-20 w-96 h-96 rounded-full blur-3xl" style="background-color: var(--dark-accent-purple); opacity: 0.15;"></div>
  <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[400px] rounded-full blur-3xl" style="background: radial-gradient(circle, var(--dark-purple-700) 0%, transparent 70%); opacity: 0.1;"></div>


  
  <app-navbar></app-navbar>
  <div class="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 relative z-10">
    <div class="text-center max-w-4xl">
      <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 theme-gradient-text leading-tight">
        Welcome to My Portfolio
      </h1>
      <p class="text-lg sm:text-xl lg:text-2xl mb-10 theme-text-secondary font-light max-w-2xl mx-auto leading-relaxed">
        Full Stack Developer & Creative Problem Solver
      </p>
     
    </div>
  </div>
</div>
