<section style="padding-top: 30px"   class="flex items-center flex-col  w-full  ">
 
  <div class="flex mt-20">
      <h1 class="text-4xl font-semibold text-center theme-gradient-text ">Tools & Technologies</h1>
  </div>


<div style="padding: 20px;" class="grid grid-cols-1 md:grid-cols-4 gap-6  md:justify-evenly ">
    <div style="padding: 20px;" class=" flex flex-col md:p-[30px] gap-5  border border-white/20 
            rounded-xl 
            bg-gradient-to-r from-gray-800/40 to-gray-900/40 
            backdrop-blur-md   ">
    <h1 class="text-2xl font-semibold text-center">Frontend</h1>

   <div class="grid grid-cols-3 gap-4  ">
  @for (item of frontend; track $index; let i = $index) {
    <div
      class="flex flex-col gap-4 items-center
        {{ i === frontend.length - 1 && frontend.length % 3 === 1 ? 'col-span-3 justify-self-center' : '' }}">
      <img
        src="{{ item.iconUrl }}"
        alt="{{ item.name }} logo"
        class="h-10 filter {{ item.name === 'Angular' ? 'invert' : 'invert-0' }}"
      />
      <p class="text-sm font-light">{{ item.name }}</p>
    </div>
  }
</div>
  </div>
  <div style="padding: 20px;" class="flex gap-5 items-center flex-col border border-white/20 
            rounded-xl 
            bg-gradient-to-r from-gray-800/40 to-gray-900/40 
            backdrop-blur-md 
            
          ">
    <h1 class="text-2xl font-semibold">Backend</h1>
    <div class="flex justify-evenly w-full md:gap-6">
        @for (item of backend; track $index) {
        <div class="flex flex-col gap-4 items-center">
            <img
      src="{{ item.iconUrl }}"
      class="h-10  filter {{ item.name === 'Express' ? 'invert' : 'invert-0' }}"
      alt="logo"
    />
    <p>{{ item.name }}</p>
        </div>
    
    }
    </div>
    
  </div>
  <div style="padding: 20px;" class="flex gap-5 items-center flex-col border border-white/20 
            rounded-xl 
            bg-gradient-to-r from-gray-800/40 to-gray-900/40 
            backdrop-blur-md ">
    <h1 class="text-2xl font-semibold">Databases</h1>
    <div class="flex justify-evenly w-full md:gap-6">
         @for (item of databases; track $index) { 
        <div class="flex flex-col gap-4 items-center">
               <img src="{{ item.iconUrl }}" alt="logo" class="h-10" />
    <p>{{ item.name }}</p>
        </div>
 
    }
    </div>
   
  </div>
  <div style="padding: 20px;" class="flex gap-5 items-center flex-col border border-white/20 
            rounded-xl 
            bg-gradient-to-r from-gray-800/40 to-gray-900/40 
            backdrop-blur-md ">
    <h1 class="text-2xl font-semibold">Tools & Others</h1>
    <div class="grid grid-cols-3 gap-5  md:gap-5" >
         @for (item of toolsAndOthers; track $index) {
        <div class="flex flex-col gap-4 items-center">   <img src="{{ item.iconUrl }}" alt="logo" class="h-10" />
    <p>{{ item.name }}</p></div>
 
    }
    </div>
   
  </div>
</div>



</section>
