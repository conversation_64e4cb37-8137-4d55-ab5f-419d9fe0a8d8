<section id="#skills" class="flex items-center gap-y-6 flex-col  w-full h-auto ">
 
  <div class="flex">
      <h1 class="text-4xl font-semibold text-center theme-gradient-text ">Tools & Technologies</h1>
  </div>
 

<div class="flex flex-col gap-10 md:flex-row  md:w-full  md:justify-evenly">
    <div class="flex gap-5 items-center flex-col">
    <h1 class="text-2xl font-semibold">Frontend</h1>

   <div class="grid grid-cols-3 gap-4 ">
  @for (item of frontend; track $index; let i = $index) {
    <div
      class="flex flex-col gap-4 items-center
        {{ i === frontend.length - 1 && frontend.length % 3 === 1 ? 'col-span-3 justify-self-center' : '' }}">
      <img
        src="{{ item.iconUrl }}"
        alt="{{ item.name }} logo"
        class="h-10 filter {{ item.name === 'Angular' ? 'invert' : 'invert-0' }}"
      />
      <p class="text-sm">{{ item.name }}</p>
    </div>
  }
</div>
  </div>
  <div class="flex gap-5 items-center flex-col ">
    <h1 class="text-2xl font-semibold">Backend</h1>
    <div class="flex justify-evenly w-full md:gap-6">
        @for (item of backend; track $index) {
        <div class="flex flex-col gap-4 items-center">
            <img
      src="{{ item.iconUrl }}"
      class="h-10  filter {{ item.name === 'Express' ? 'invert' : 'invert-0' }}"
      alt="logo"
    />
    <p>{{ item.name }}</p>
        </div>
    
    }
    </div>
    
  </div>
  <div class="flex gap-5 items-center flex-col">
    <h1 class="text-2xl font-semibold">Databases</h1>
    <div class="flex justify-evenly w-full md:gap-6">
         @for (item of databases; track $index) { 
        <div class="flex flex-col gap-4 items-center">
               <img src="{{ item.iconUrl }}" alt="logo" class="h-10" />
    <p>{{ item.name }}</p>
        </div>
 
    }
    </div>
   
  </div>
  <div class="flex gap-5 items-center flex-col">
    <h1 class="text-2xl font-semibold">Tools & Others</h1>
    <div class="grid grid-cols-3 gap-5  md:gap-5" >
         @for (item of toolsAndOthers; track $index) {
        <div class="flex flex-col gap-4 items-center">   <img src="{{ item.iconUrl }}" alt="logo" class="h-10" />
    <p>{{ item.name }}</p></div>
 
    }
    </div>
   
  </div>
</div>



</section>
