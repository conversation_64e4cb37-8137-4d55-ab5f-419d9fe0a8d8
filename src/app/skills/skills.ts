import { Component } from '@angular/core';

@Component({
  selector: 'app-skills',
  imports: [],
  templateUrl: './skills.html',
  styleUrl: './skills.css',
})
export class Skills {
readonly frontend = [
  { name: 'HTML',        iconUrl: 'https://cdn.simpleicons.org/html5' },
  { name: 'CSS',         iconUrl: 'https://cdn.simpleicons.org/css' },
  { name: 'JavaScript',  iconUrl: 'https://cdn.simpleicons.org/javascript' },
  { name: 'TypeScript',  iconUrl: 'https://cdn.simpleicons.org/typescript' },
  { name: 'Angular',     iconUrl: 'https://cdn.simpleicons.org/angular' },
  { name: '<PERSON><PERSON><PERSON>CS<PERSON>', iconUrl: 'https://cdn.simpleicons.org/tailwindcss' },
  
  { name: 'GSAP',        iconUrl: 'https://cdn.simpleicons.org/greensock' },
];

readonly backend = [
  { name: 'Node.js', iconUrl: 'https://cdn.simpleicons.org/nodedotjs' },
  { name: 'Express', iconUrl: 'https://cdn.simpleicons.org/express' },
  { name: 'NestJS',  iconUrl: 'https://cdn.simpleicons.org/nestjs' },
];

readonly databases = [
  { name: 'PostgreSQL', iconUrl: 'https://cdn.simpleicons.org/postgresql' },
  { name: 'MongoDB',    iconUrl: 'https://cdn.simpleicons.org/mongodb' },
  { name: 'MySQL',      iconUrl: 'https://cdn.simpleicons.org/mysql' },
];

readonly toolsAndOthers = [
  { name: 'Git',      iconUrl: 'https://cdn.simpleicons.org/git' },
  { name: 'Linux',    iconUrl: 'https://cdn.simpleicons.org/linux' },
  { name: 'Postman',  iconUrl: 'https://cdn.simpleicons.org/postman' },
  { name: 'TypeORM',  iconUrl: 'https://cdn.simpleicons.org/typeorm' },
  { name: 'Java',     iconUrl: 'java.png' },
  {name:'Nvim',iconUrl:'https://cdn.simpleicons.org/neovim'}
];

}
