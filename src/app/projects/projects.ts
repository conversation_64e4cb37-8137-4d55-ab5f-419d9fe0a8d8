import { Component } from '@angular/core';

@Component({
  selector: 'app-projects',
  imports: [],
  templateUrl: './projects.html',
  styleUrl: './projects.css'
})
export class Projects {

readonly projectsList = [
  {
    name: "Examination Duty Allotment System",
    description:
      "Web-based system to automate invigilation duty allocation, ensuring fair distribution and reducing manual effort.",
    tools: [
      { name: "React", iconUrl: "https://cdn.simpleicons.org/react" },
      { name: "TailwindCSS", iconUrl: "https://cdn.simpleicons.org/tailwindcss" },
      { name: "Express", iconUrl: "https://cdn.simpleicons.org/express" },
      { name: "MySQL", iconUrl: "https://cdn.simpleicons.org/mysql" },
      { name: "Git", iconUrl: "https://cdn.simpleicons.org/git" }
    ],
    imageUrl: ""
  },
  {
    name: "Angular-NestJS Blog App",
    description:
      "Full-stack blog platform with Angular 18 frontend and NestJS backend, using TypeORM with PostgreSQL for efficient data handling.",
    tools: [
      { name: "Angular", iconUrl: "https://cdn.simpleicons.org/angular" },
      { name: "NestJS", iconUrl: "https://cdn.simpleicons.org/nestjs" },
      { name: "TypeORM", iconUrl: "https://avatars.githubusercontent.com/u/20165699?s=200&v=4" }, // not in Simple Icons
      { name: "PostgreSQL", iconUrl: "https://cdn.simpleicons.org/postgresql" },
      { name: "Git", iconUrl: "https://cdn.simpleicons.org/git" }
    ],
    imageUrl: "",
    githubUrl: ""
  },
  {
    name: "Portfolio Website",
    description: "My portfolio website built with Angular and TailwindCSS.",
    tools: [
      { name: "Angular", iconUrl: "https://cdn.simpleicons.org/angular" },
      { name: "TailwindCSS", iconUrl: "https://cdn.simpleicons.org/tailwindcss" },
      { name: "Git", iconUrl: "https://cdn.simpleicons.org/git" },
      { name: "GSAP", iconUrl: "https://cdn.simpleicons.org/greensock" },
     
      { name: "Three.js", iconUrl: "https://cdn.simpleicons.org/threedotjs" }
    ],
    imageUrl: "",
    githubUrl: "",
    liveUrl: ""
  },
  {
    name: "Pokedex",
    description:
      "Its a simple Pokedex built using Angular & Tailwindcss in frontend. It utilized PokeAPI developed by Nintendo. Angular's Built in HTTP module along with RxJs fetched pokemon data from PokemonAPI.",
    tools: [
      { name: "Angular", iconUrl: "https://cdn.simpleicons.org/angular" },
      { name: "TailwindCSS", iconUrl: "https://cdn.simpleicons.org/tailwindcss" },
      { name: "PokeAPI", iconUrl: "https://raw.githubusercontent.com/PokeAPI/media/master/logo/pokeapi_256.png" } // not in Simple Icons
    ],
    imageUrl: "",
    githubUrl: "",
    liveUrl: ""
  },
  {
    name: "Question Paper Generating System",
    description:
      "Web-based system to automate question paper generation, ensuring fair distribution and reducing manual effort.",
    tools: [
      { name: "React", iconUrl: "https://cdn.simpleicons.org/react" },
      { name: "Express", iconUrl: "https://cdn.simpleicons.org/express" },
      { name: "Node.js", iconUrl: "https://cdn.simpleicons.org/nodedotjs" },
      { name: "MongoDB", iconUrl: "https://cdn.simpleicons.org/mongodb" },
      { name: "Git", iconUrl: "https://cdn.simpleicons.org/git" }
    ],
    imageUrl: "",
    githubUrl: "",
    liveUrl: ""
  },
  {
    name: "Bookshelf.in",
    description: "A book management system built using HTML,CSS & Js. It utilizes MySQL as the database.",
    tools: [
      { name: "HTML5", iconUrl: "https://cdn.simpleicons.org/html5" },
      { name: "CSS3", iconUrl: "https://cdn.simpleicons.org/css" },
      { name: "JavaScript", iconUrl: "https://cdn.simpleicons.org/javascript" },
      { name: "PHP", iconUrl: "https://cdn.simpleicons.org/php" },
      { name: "XAMPP", iconUrl: "https://cdn.simpleicons.org/xampp" },
      { name: "MySQL", iconUrl: "https://cdn.simpleicons.org/mysql" }
    ],
    imageUrl: "",
    githubUrl: "",
    liveUrl: ""
  }
];



}
