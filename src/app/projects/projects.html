<section id="#projects" class="flex-1 items-center justify-evenly w-full h-auto">
    <h1 class="text-4xl font-semibold text-center theme-gradient-text">Projects</h1>
    <div class="grid grid-cols-1 gap-7 ">
        @for (item of projectsList; track $index) {
            <div class="flex flex-col border-[1px] border-gray-400 rounded-sm ">
                <h1 class="text-2xl font-semibold">{{item.name}}</h1>
                <p>{{item.description}}</p>
                <div class="flex ">
                    @for (tool of item.tools; track $index) {
                     <div class=" p-2 m-2">
                        <img
        src="{{ tool.iconUrl }}"
        alt="{{ tool.name }} logo"
        class="h-6 filter {{ tool.name === 'Angular'|| tool.name==='Three.js' || tool.name==='Express' ? 'invert' : 'invert-0' }}"
      />
                     </div>
                        
                    }
                </div>
                    
            
            </div>
        }
    </div>
</section>
