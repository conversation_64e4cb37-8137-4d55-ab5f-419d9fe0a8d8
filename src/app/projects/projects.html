<div style="padding-top: 30px">
  <h1 class="text-4xl font-semibold text-center theme-gradient-text">Projects</h1>
  <div class="container">
    <div style="padding: 20px" class="grid grid-cols-1 md:grid-cols-2 gap-5">
      @for (item of projectsList; track $index) {
      <div
        style="padding: 20px"
        class="flex flex-col border border-white/20 rounded-xl bg-gradient-to-r from-gray-800/40 to-gray-900/40 backdrop-blur-md gap-5"
      >
        <div class="w-auto flex justify-center">
          <div class="rounded-lg flex md:justify-center overflow-hidden h-fit w-fit">
            <img src="{{ item.imageUrl }}" class="md:h-40 h-40" alt="project-image" />
          </div>
        </div>

        <h1 class="text-xl font-semibold">{{ item.name }}</h1>
        <p class="text-white/85">{{ item.description }}</p>
        <hr class="border-t border-gray-500/30 my-4" />

        <div class="flex gap-3 flex-col">
          <h3>Tools used :</h3>

          <div class="flex gap-3">
            @for (tool of item.tools; track $index) {
            <img
              src="{{ tool.iconUrl }}"
              alt="{{ tool.name }} logo"
              class="h-6 filter {{
                tool.name === 'Angular' || tool.name === 'Three.js' || tool.name === 'Express'
                  ? 'invert'
                  : 'invert-0'
              }}"
            />
            }
          </div>
        </div>
        <hr class="border-t border-gray-500/30 my-4" />
        <div class="flex gap-5">
          <div
            class="flex gap-1 btn items-center border border-white/20 rounded-md hover:bg-white/10"
          >
            <img src="https://cdn.simpleicons.org/github" class="filter h-4 invert" alt="" />
            <a [href]="item.githubUrl || null"> GitHub</a>
          </div>
          <div
            class="btn flex gap-1 items-center w-auto border border-white/20 rounded-md hover:bg-white/10"
          >
            <img src="https://cdn.simpleicons.org/googlemaps" class="filter h-4" alt="" />
            <a [href]="item.liveUrl || null">Live</a>
          </div>
        </div>
      </div>
      }
    </div>
  </div>
</div>
