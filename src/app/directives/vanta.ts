import { Directive, Input,ElementRef } from '@angular/core';
import * as THREE from 'three';
import NET from 'vanta/dist/vanta.net.min';

@Directive({
  selector: '[appVanta]'
})
export class Vanta {

@Input() options:any
private vantaEffect: any;

  constructor(private el: ElementRef) {}

  ngOnInit(): void {
    this.vantaEffect = NET({
      el: this.el.nativeElement,
      THREE,
      mouseControls: true,
      touchControls: true,
      gyroControls: false,
      minHeight: 200.00,
      minWidth: 200.00,
      scale: 1.00,
      scaleMobile: 1.00,
      ...this.options
    });
  }

  ngOnDestroy(): void {
    if (this.vantaEffect) {
      this.vantaEffect.destroy();
    }
  }

}
