{"name": "portfolio-ng", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "prettier": {"printWidth": 100, "singleQuote": true, "overrides": [{"files": "*.html", "options": {"parser": "angular"}}]}, "private": true, "dependencies": {"@angular/animations": "^20.2.4", "@angular/common": "^20.2.0", "@angular/compiler": "^20.2.0", "@angular/core": "^20.2.0", "@angular/forms": "^20.2.0", "@angular/platform-browser": "^20.2.0", "@angular/router": "^20.2.0", "@omnedia/ngx-aurora": "^3.0.0", "@tailwindcss/postcss": "^4.1.12", "@tailwindcss/vite": "^4.1.12", "postcss": "^8.5.6", "primeicons": "^7.0.0", "primeng": "^20.1.1", "rxjs": "~7.8.0", "tailwindcss": "^4.1.12", "three": "^0.180.0", "tslib": "^2.3.0", "vanta": "^0.5.24"}, "devDependencies": {"@angular/build": "^20.2.2", "@angular/cli": "^20.2.2", "@angular/compiler-cli": "^20.2.0", "@types/jasmine": "~5.1.0", "@types/three": "^0.180.0", "jasmine-core": "~5.9.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.9.2"}}