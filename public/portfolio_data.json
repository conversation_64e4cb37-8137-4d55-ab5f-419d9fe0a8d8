{"name": "<PERSON><PERSON><PERSON>", "contact": {"email": "<EMAIL>", "phone": "+91 9535171483", "address": "4-137 Padyar House, Tenkayedapadavu", "portfolio": "https://praneethkumar189.github.io/Portfolio/", "github": "https://github.com/PraneethKumar189"}, "profile": "Enthusiastic developer with hands-on experience in Angular, NestJS, and Linux administration. Adept at creating dynamic user interfaces and robust back-end APIs. Focused on delivering high-quality solutions and growing as a full-stack developer.", "education": [{"degree": "Master of Computer Applications", "institution": "St Joseph Engineering College, Vamanjoor", "duration": "Aug 2023 – Aug 2025", "location": "Mangalore"}, {"degree": "Bachelor of Science in Computer Science", "institution": "Dr. <PERSON><PERSON> - <PERSON><PERSON> Government First Grade College", "duration": "Jun 2019 – Oct 2022", "location": "Mangalore"}], "experience": [{"company": "Softionik Solutions Pvt Ltd", "role": "Full Stack Developer Intern", "duration": "May 2025 – Aug 2025", "location": "Mangalore", "responsibilities": ["Developed web applications using Angular and NestJS", "Implemented database operations with TypeORM and PostgreSQL", "Built REST APIs for core functionalities"]}], "languages": ["English", "Hindi", "Kannada", "Konkani", "<PERSON><PERSON>"], "interests": ["Reading Books", "Volleyball", "Cricket", "Chess"], "skills": {"frontend": ["HTML", "CSS", "JavaScript", "TypeScript", "TailwindCSS", "Angular"], "backend": ["Node.js", "Express", "NestJS"], "database": ["PostgreSQL", "MongoDB", "MySQL"], "toolsAndOthers": ["Git", "Linux", "Postman", "TypeORM", "Java"]}, "projects": [{"title": "Examination Duty Allotment System", "duration": "May 2025 – Aug 2025", "description": "Web-based system to automate invigilation duty allocation, ensuring fair distribution and reducing manual effort. Built with React, TailwindCSS, Express, MySQL, and Git."}, {"title": "Angular-NestJS Blog App", "duration": "Nov 2024 – Dec 2024", "description": "Full-stack blog platform with Angular 18 frontend and NestJS backend, using TypeORM with PostgreSQL for efficient data handling."}]}